<template>
  <view class="page flex-col">
    <view class="section_1 flex-col justify-between">
      <view class="box_1 flex-row">
        <text class="text_1">12:30</text>
        <image
          class="thumbnail_1"
          referrerpolicy="no-referrer"
          src="/static/myfollowing/FigmaDDSSlicePNGd7f3f0a8da68df76a531af0fb9fddfd7.png"
        />
        <image
          class="thumbnail_2"
          referrerpolicy="no-referrer"
          src="/static/myfollowing/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png"
        />
        <image
          class="thumbnail_3"
          referrerpolicy="no-referrer"
          src="/static/myfollowing/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png"
        />
      </view>
      <view class="box_2 flex-row">
        <image
          class="thumbnail_4"
          referrerpolicy="no-referrer"
          src="/static/myfollowing/FigmaDDSSlicePNGb2c0f043641f22ede4595acd72b9777f.png"
        />
        <text class="text_2">我的关注</text>
        <image
          class="image_1"
          referrerpolicy="no-referrer"
          src="/static/myfollowing/FigmaDDSSlicePNGb8b4476965cc16cde5ebd2a73f016520.png"
        />
      </view>
    </view>
    <view class="section_2 flex-col">
      <view class="list_1 flex-col">
        <view
          class="list-items_1 flex-row"
          v-for="(item, index) in loopData0"
          :key="index"
        >
          <view class="image-text_1 flex-row justify-between">
            <image
              class="image_2"
              referrerpolicy="no-referrer"
              :src="item.lanhuimage0"
            />
            <view class="text-group_1 flex-col justify-between">
              <text class="text_3" v-html="item.lanhutext0"></text>
              <view class="text-wrapper_1">
                <text class="text_4" v-html="item.lanhutext1"></text>
                <text class="text_5" v-html="item.lanhutext2"></text>
                <text class="text_6" v-html="item.lanhutext3"></text>
                <text class="text_7" v-html="item.lanhutext4"></text>
              </view>
            </view>
          </view>
          <text
            v-if="item.slot1 === 1"
            class="text_8"
            v-html="item.specialSlot1.lanhutext0"
          ></text>
          <view
            v-if="item.slot2 === 2"
            class="image-text_2 flex-row justify-between"
          >
            <image
              class="thumbnail_5"
              referrerpolicy="no-referrer"
              :src="item.specialSlot2.lanhuimage0"
            />
            <text
              class="text-group_2"
              v-html="item.specialSlot2.lanhutext0"
            ></text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  data() {
    return {
      loopData0: [
        {
          lanhuimage0:
            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG1b4ca1241e4246a1f64fa2b5efcdb826.png',
          lanhutext0: 'Libero',
          lanhutext1: '已结单',
          lanhutext2: '2514&nbsp;&nbsp;',
          lanhutext3: '粉丝',
          lanhutext4: '&nbsp;2',
          specialSlot2: {
            lanhuimage0:
              'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGaef8a291a2fd0061d62323e74926fef5.png',
            lanhutext0: '0M'
          },
          slot2: 2
        },
        {
          lanhuimage0:
            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG1b4ca1241e4246a1f64fa2b5efcdb826.png',
          lanhutext0: 'Libero',
          lanhutext1: '已结单',
          lanhutext2: '2514&nbsp;&nbsp;',
          lanhutext3: '粉丝',
          lanhutext4: '&nbsp;2',
          specialSlot1: { lanhutext0: '0M' },
          slot1: 1
        },
        {
          lanhuimage0:
            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG1b4ca1241e4246a1f64fa2b5efcdb826.png',
          lanhutext0: 'Libero',
          lanhutext1: '已结单',
          lanhutext2: '2514&nbsp;&nbsp;',
          lanhutext3: '粉丝',
          lanhutext4: '&nbsp;2',
          specialSlot2: {
            lanhuimage0:
              'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGaef8a291a2fd0061d62323e74926fef5.png',
            lanhutext0: '0M'
          },
          slot2: 2
        },
        {
          lanhuimage0:
            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG1b4ca1241e4246a1f64fa2b5efcdb826.png',
          lanhutext0: 'Libero',
          lanhutext1: '已结单',
          lanhutext2: '2514&nbsp;&nbsp;',
          lanhutext3: '粉丝',
          lanhutext4: '&nbsp;2',
          specialSlot1: { lanhutext0: '0M' },
          slot1: 1
        }
      ],
      constants: {}
    };
  },
  methods: {}
};
</script>
<style lang='scss'>
@import '../common/common.scss';
@import './assets/style/index.rpx.scss';
</style>
