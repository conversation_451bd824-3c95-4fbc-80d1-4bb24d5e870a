.page {
  background-color: rgba(247, 247, 247, 1);
  position: relative;
  width: 10rem;
  height: 21.654rem;
  overflow: hidden;
  .section_1 {
    background-color: rgba(255, 255, 255, 1);
    width: 10rem;
    height: 2.294rem;
    .box_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 10rem;
      height: 0.854rem;
      .text_1 {
        width: 0.854rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.32rem;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 4rem;
        margin: 0.187rem 0 0 0.427rem;
      }
      .thumbnail_1 {
        width: 0.48rem;
        height: 0.48rem;
        margin: 0.187rem 0 0 6.694rem;
      }
      .thumbnail_2 {
        width: 0.48rem;
        height: 0.48rem;
        margin: 0.187rem 0 0 0.08rem;
      }
      .thumbnail_3 {
        width: 0.507rem;
        height: 0.507rem;
        margin: 0.187rem 0.4rem 0 0.08rem;
      }
    }
    .box_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 10rem;
      height: 1.44rem;
      .thumbnail_4 {
        width: 0.24rem;
        height: 0.454rem;
        margin: 0.507rem 0 0 0.48rem;
      }
      .text_2 {
        width: 1.707rem;
        height: 0.587rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.426rem;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 0.587rem;
        margin: 0.427rem 0 0 0.294rem;
      }
      .image_1 {
        width: 2.32rem;
        height: 0.854rem;
        margin: 0.294rem 0.16rem 0 4.8rem;
      }
    }
  }
  .section_2 {
    width: 10rem;
    height: 19.387rem;
    margin-bottom: 0.027rem;
    .list_1 {
      width: 9.36rem;
      height: 8.694rem;
      justify-content: space-between;
      margin: 0.267rem 0 0 0.32rem;
      .list-items_1 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 10px;
        width: 9.36rem;
        height: 1.974rem;
        margin-bottom: 0.267rem;
        .image-text_1 {
          width: 4.614rem;
          height: 1.334rem;
          margin: 0.32rem 0 0 0.32rem;
          .image_2 {
            width: 1.334rem;
            height: 1.334rem;
          }
          .text-group_1 {
            width: 3.04rem;
            height: 0.987rem;
            margin-top: 0.16rem;
            .text_3 {
              width: 1.2rem;
              height: 0.4rem;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 0.4rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 2.667rem;
            }
            .text-wrapper_1 {
              width: 3.04rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              font-size: 0;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 2.667rem;
              margin-top: 0.267rem;
              .text_4 {
                width: 3.04rem;
                height: 0.32rem;
                overflow-wrap: break-word;
                color: rgba(102, 102, 102, 1);
                font-size: 0.32rem;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 2.667rem;
              }
              .text_5 {
                width: 3.04rem;
                height: 0.32rem;
                overflow-wrap: break-word;
                color: rgba(11, 206, 148, 1);
                font-size: 0.32rem;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 2.667rem;
              }
              .text_6 {
                width: 3.04rem;
                height: 0.32rem;
                overflow-wrap: break-word;
                color: rgba(102, 102, 102, 1);
                font-size: 0.32rem;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 2.667rem;
              }
              .text_7 {
                width: 3.04rem;
                height: 0.32rem;
                overflow-wrap: break-word;
                color: rgba(11, 206, 148, 1);
                font-size: 0.32rem;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 2.667rem;
              }
            }
          }
        }
        .text_8 {
          width: 0.454rem;
          height: 0.32rem;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 0.32rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 2.667rem;
          margin: 0.827rem 0.32rem 0 3.654rem;
        }
        .image-text_2 {
          width: 0.88rem;
          height: 0.347rem;
          margin: 0.8rem 0.32rem 0 3.227rem;
          .thumbnail_5 {
            width: 0.347rem;
            height: 0.347rem;
          }
          .text-group_2 {
            width: 0.454rem;
            height: 0.32rem;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 2.667rem;
            margin-top: 0.027rem;
          }
        }
      }
    }
  }
}
