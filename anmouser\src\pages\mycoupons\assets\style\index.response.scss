.page {
  background-color: rgba(247, 247, 247, 1);
  position: relative;
  width: 100vw;
  height: 216.54vw;
  overflow: hidden;
  .group_1 {
    background-color: rgba(255, 255, 255, 1);
    width: 100vw;
    height: 22.94vw;
    .box_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 100vw;
      height: 8.54vw;
      .text_1 {
        width: 8.54vw;
        height: 4.8vw;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 3.2vw;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 40vw;
        margin: 1.86vw 0 0 4.26vw;
      }
      .thumbnail_1 {
        width: 4.8vw;
        height: 4.8vw;
        margin: 1.86vw 0 0 66.93vw;
      }
      .thumbnail_2 {
        width: 4.8vw;
        height: 4.8vw;
        margin: 1.86vw 0 0 0.8vw;
      }
      .thumbnail_3 {
        width: 5.07vw;
        height: 5.07vw;
        margin: 1.86vw 4vw 0 0.8vw;
      }
    }
    .box_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 100vw;
      height: 14.4vw;
      .thumbnail_4 {
        width: 2.4vw;
        height: 4.54vw;
        margin: 5.06vw 0 0 4.8vw;
      }
      .text_2 {
        width: 17.07vw;
        height: 5.87vw;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 4.26vw;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 5.87vw;
        margin: 4.26vw 0 0 2.93vw;
      }
      .image_1 {
        width: 23.2vw;
        height: 8.54vw;
        margin: 2.93vw 1.6vw 0 48vw;
      }
    }
  }
  .group_2 {
    position: relative;
    width: 100vw;
    height: 193.87vw;
    margin-bottom: 0.27vw;
    .box_3 {
      background-color: rgba(255, 255, 255, 1);
      height: 11.74vw;
      width: 100vw;
      .text-wrapper_1 {
        width: 78.67vw;
        height: 5.87vw;
        margin: 2.93vw 0 0 10.66vw;
        .text_3 {
          width: 12vw;
          height: 5.87vw;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 4vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 4vw;
        }
        .text_4 {
          width: 12vw;
          height: 5.87vw;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 4vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 4vw;
          margin-left: 21.34vw;
        }
        .text_5 {
          width: 12vw;
          height: 5.87vw;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 4vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 4vw;
          margin-left: 21.34vw;
        }
      }
      .section_1 {
        width: 12vw;
        height: 0.8vw;
        margin: 2.13vw 0 0 10.66vw;
        .section_2 {
          background-color: rgba(11, 206, 148, 1);
          width: 12vw;
          height: 0.8vw;
        }
      }
    }
    .text-wrapper_2 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0px 0px 8px 8px;
      width: 93.6vw;
      height: 37.6vw;
      margin: 27.2vw 0 0 3.2vw;
      .text_6 {
        width: 23.74vw;
        height: 4.8vw;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 3.2vw;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 3.2vw;
        margin: 5.86vw 0 0 3.2vw;
      }
      .text_7 {
        width: 23.74vw;
        height: 4.8vw;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 3.2vw;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 3.2vw;
        margin: 1.6vw 0 0 3.2vw;
      }
      .text_8 {
        width: 34.94vw;
        height: 4.8vw;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 3.2vw;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 3.2vw;
        margin: 1.6vw 0 0 3.2vw;
      }
      .text_9 {
        width: 21.07vw;
        height: 4.8vw;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 3.2vw;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 3.2vw;
        margin: 0.8vw 0 0 16.8vw;
      }
      .text_10 {
        width: 21.07vw;
        height: 4.8vw;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 3.2vw;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 3.2vw;
        margin: 0.8vw 0 2.93vw 16.26vw;
      }
    }
    .box_4 {
      width: 93.6vw;
      height: 26.4vw;
      margin: 2.66vw 0 0 3.2vw;
      .section_3 {
        width: 24.8vw;
        height: 26.4vw;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNGdc7b1952f4040caabd55890af14df0e1.png)
          100% no-repeat;
        background-size: 100% 100%;
        .text-group_1 {
          width: 20vw;
          height: 20.27vw;
          margin: 4.53vw 0 0 1.86vw;
          .text-wrapper_3 {
            width: 20vw;
            height: 10.14vw;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            .text_11 {
              width: 20vw;
              height: 10.14vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 2.66vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
            }
            .text_12 {
              width: 20vw;
              height: 10.14vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 6.93vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 6.94vw;
            }
          }
          .text_13 {
            width: 9.6vw;
            height: 4.8vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2vw;
            margin: 5.33vw 0 0 5.86vw;
          }
        }
      }
      .section_4 {
        background-color: rgba(255, 255, 255, 1);
        height: 26.4vw;
        width: 68.8vw;
        .box_5 {
          width: 62.14vw;
          height: 12.54vw;
          margin: 3.46vw 0 0 3.46vw;
          .text-group_2 {
            width: 24.54vw;
            height: 12.54vw;
            .text_14 {
              width: 24.54vw;
              height: 5.87vw;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 4vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 4vw;
            }
            .text_15 {
              width: 9.87vw;
              height: 4.8vw;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 3.2vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 3.2vw;
              margin-top: 1.87vw;
            }
          }
          .text-wrapper_4 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 5px;
            height: 6.4vw;
            margin-top: 2.94vw;
            width: 17.07vw;
            .text_16 {
              width: 12.8vw;
              height: 4.54vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 3.2vw;
              margin: 1.06vw 0 0 2.13vw;
            }
          }
        }
        .box_6 {
          width: 62.14vw;
          height: 4.8vw;
          margin: 4vw 0 1.6vw 3.46vw;
          .text_17 {
            width: 33.6vw;
            height: 4.8vw;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2vw;
          }
          .thumbnail_5 {
            width: 1.6vw;
            height: 2.67vw;
            margin-top: 2.14vw;
          }
        }
      }
    }
    .box_7 {
      width: 93.6vw;
      height: 26.4vw;
      margin: 2.66vw 0 0 3.2vw;
      .section_5 {
        width: 24.8vw;
        height: 26.4vw;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNGa8ae8798db599178c2c049244bfc2db7.png)
          100% no-repeat;
        background-size: 100% 100%;
        .text-group_3 {
          width: 20vw;
          height: 20.27vw;
          margin: 4.53vw 0 0 1.86vw;
          .text-wrapper_5 {
            width: 20vw;
            height: 10.14vw;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            .text_18 {
              width: 20vw;
              height: 10.14vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 2.66vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
            }
            .text_19 {
              width: 20vw;
              height: 10.14vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 6.93vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 6.94vw;
            }
          }
          .text_20 {
            width: 16vw;
            height: 4.8vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2vw;
            margin: 5.33vw 0 0 2.4vw;
          }
        }
      }
      .section_6 {
        height: 26.4vw;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNG4859356b7546855289788c69a3294a6b.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 68.8vw;
        .box_8 {
          width: 62.4vw;
          height: 12.54vw;
          margin: 3.46vw 0 0 3.46vw;
          .text-group_4 {
            width: 34.67vw;
            height: 12.54vw;
            .text_21 {
              width: 34.67vw;
              height: 5.87vw;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 4vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 4vw;
            }
            .text_22 {
              width: 12.8vw;
              height: 4.8vw;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 3.2vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 3.2vw;
              margin-top: 1.87vw;
            }
          }
          .section_7 {
            background-color: rgba(153, 153, 153, 1);
            width: 12.8vw;
            height: 9.6vw;
            margin-top: 1.34vw;
          }
        }
        .text-wrapper_6 {
          width: 33.6vw;
          height: 4.8vw;
          margin: 4.26vw 0 1.33vw 3.46vw;
          .text_23 {
            width: 33.6vw;
            height: 4.8vw;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2vw;
          }
        }
      }
    }
    .box_9 {
      width: 93.6vw;
      height: 26.4vw;
      margin: 2.66vw 0 30.13vw 3.2vw;
      .box_10 {
        width: 24.8vw;
        height: 26.4vw;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNGa8ae8798db599178c2c049244bfc2db7.png)
          100% no-repeat;
        background-size: 100% 100%;
        .text-group_5 {
          width: 20vw;
          height: 20.27vw;
          margin: 4.53vw 0 0 1.86vw;
          .text-wrapper_7 {
            width: 20vw;
            height: 10.14vw;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            .text_24 {
              width: 20vw;
              height: 10.14vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 2.66vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
            }
            .text_25 {
              width: 20vw;
              height: 10.14vw;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 6.93vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 6.94vw;
            }
          }
          .text_26 {
            width: 16vw;
            height: 4.8vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2vw;
            margin: 5.33vw 0 0 2.4vw;
          }
        }
      }
      .box_11 {
        height: 26.4vw;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNG4859356b7546855289788c69a3294a6b.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 68.8vw;
        .box_12 {
          width: 62.67vw;
          height: 12.54vw;
          margin: 3.46vw 0 0 3.46vw;
          .text-group_6 {
            width: 34.67vw;
            height: 12.54vw;
            .text_27 {
              width: 34.67vw;
              height: 5.87vw;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 4vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 4vw;
            }
            .text_28 {
              width: 12.8vw;
              height: 4.8vw;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 3.2vw;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 3.2vw;
              margin-top: 1.87vw;
            }
          }
          .text-wrapper_8 {
            background-color: rgba(153, 153, 153, 1);
            height: 9.6vw;
            margin-top: 1.87vw;
            width: 12.8vw;
            .text_29 {
              width: 8.8vw;
              height: 2.14vw;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 2.93vw;
              font-family: Source Han Sans CN-Bold;
              font-weight: 700;
              text-align: left;
              white-space: nowrap;
              line-height: 2.94vw;
              margin: 2.66vw 0 0 2.4vw;
            }
          }
        }
        .text-wrapper_9 {
          width: 33.6vw;
          height: 4.8vw;
          margin: 4.26vw 0 1.33vw 3.46vw;
          .text_30 {
            width: 33.6vw;
            height: 4.8vw;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2vw;
          }
        }
      }
    }
    .box_13 {
      position: absolute;
      left: 3.2vw;
      top: 14.67vw;
      width: 24.8vw;
      height: 26.4vw;
      background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNGdc7b1952f4040caabd55890af14df0e1.png)
        100% no-repeat;
      background-size: 100% 100%;
      .text-group_7 {
        width: 20vw;
        height: 20.27vw;
        margin: 4.53vw 0 0 1.86vw;
        .text-wrapper_10 {
          width: 20vw;
          height: 10.14vw;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          .text_31 {
            width: 20vw;
            height: 10.14vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 2.66vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
          }
          .text_32 {
            width: 20vw;
            height: 10.14vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 6.93vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 6.94vw;
          }
        }
        .text_33 {
          width: 9.6vw;
          height: 4.8vw;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 3.2vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 3.2vw;
          margin: 5.33vw 0 0 5.86vw;
        }
      }
    }
    .box_14 {
      height: 26.4vw;
      background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNG5e495abe9cd9704921ffa227bd61dae8.png)
        100% no-repeat;
      background-size: 100% 100%;
      width: 68.8vw;
      position: absolute;
      left: 28vw;
      top: 14.67vw;
      .block_1 {
        width: 62.14vw;
        height: 12.54vw;
        margin: 3.46vw 0 0 3.46vw;
        .text-group_8 {
          width: 24.54vw;
          height: 12.54vw;
          .text_34 {
            width: 24.54vw;
            height: 5.87vw;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 4vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 4vw;
          }
          .text_35 {
            width: 9.87vw;
            height: 4.8vw;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2vw;
            margin-top: 1.87vw;
          }
        }
        .text-wrapper_11 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 5px;
          height: 6.4vw;
          margin-top: 2.94vw;
          width: 17.07vw;
          .text_36 {
            width: 12.8vw;
            height: 4.54vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 3.2vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2vw;
            margin: 1.06vw 0 0 2.13vw;
          }
        }
      }
      .block_2 {
        width: 59.47vw;
        height: 6.14vw;
        margin: 4vw 0 0.26vw 3.46vw;
        .text_37 {
          width: 33.6vw;
          height: 4.8vw;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 3.2vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 3.2vw;
        }
        .thumbnail_6 {
          width: 1.6vw;
          height: 2.67vw;
          margin-top: 3.47vw;
        }
      }
    }
  }
}
