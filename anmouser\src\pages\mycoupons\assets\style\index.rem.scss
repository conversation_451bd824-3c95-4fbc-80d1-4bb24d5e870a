.page {
  background-color: rgba(247, 247, 247, 1);
  position: relative;
  width: 10rem;
  height: 21.654rem;
  overflow: hidden;
  .group_1 {
    background-color: rgba(255, 255, 255, 1);
    width: 10rem;
    height: 2.294rem;
    .box_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 10rem;
      height: 0.854rem;
      .text_1 {
        width: 0.854rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.32rem;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 4rem;
        margin: 0.187rem 0 0 0.427rem;
      }
      .thumbnail_1 {
        width: 0.48rem;
        height: 0.48rem;
        margin: 0.187rem 0 0 6.694rem;
      }
      .thumbnail_2 {
        width: 0.48rem;
        height: 0.48rem;
        margin: 0.187rem 0 0 0.08rem;
      }
      .thumbnail_3 {
        width: 0.507rem;
        height: 0.507rem;
        margin: 0.187rem 0.4rem 0 0.08rem;
      }
    }
    .box_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 10rem;
      height: 1.44rem;
      .thumbnail_4 {
        width: 0.24rem;
        height: 0.454rem;
        margin: 0.507rem 0 0 0.48rem;
      }
      .text_2 {
        width: 1.707rem;
        height: 0.587rem;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 0.426rem;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 0.587rem;
        margin: 0.427rem 0 0 0.294rem;
      }
      .image_1 {
        width: 2.32rem;
        height: 0.854rem;
        margin: 0.294rem 0.16rem 0 4.8rem;
      }
    }
  }
  .group_2 {
    position: relative;
    width: 10rem;
    height: 19.387rem;
    margin-bottom: 0.027rem;
    .box_3 {
      background-color: rgba(255, 255, 255, 1);
      height: 1.174rem;
      width: 10rem;
      .text-wrapper_1 {
        width: 7.867rem;
        height: 0.587rem;
        margin: 0.294rem 0 0 1.067rem;
        .text_3 {
          width: 1.2rem;
          height: 0.587rem;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 0.4rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.4rem;
        }
        .text_4 {
          width: 1.2rem;
          height: 0.587rem;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 0.4rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.4rem;
          margin-left: 2.134rem;
        }
        .text_5 {
          width: 1.2rem;
          height: 0.587rem;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 0.4rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.4rem;
          margin-left: 2.134rem;
        }
      }
      .section_1 {
        width: 1.2rem;
        height: 0.08rem;
        margin: 0.214rem 0 0 1.067rem;
        .section_2 {
          background-color: rgba(11, 206, 148, 1);
          width: 1.2rem;
          height: 0.08rem;
        }
      }
    }
    .text-wrapper_2 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0px 0px 8px 8px;
      width: 9.36rem;
      height: 3.76rem;
      margin: 2.72rem 0 0 0.32rem;
      .text_6 {
        width: 2.374rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 0.32rem;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.32rem;
        margin: 0.587rem 0 0 0.32rem;
      }
      .text_7 {
        width: 2.374rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 0.32rem;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.32rem;
        margin: 0.16rem 0 0 0.32rem;
      }
      .text_8 {
        width: 3.494rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 0.32rem;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.32rem;
        margin: 0.16rem 0 0 0.32rem;
      }
      .text_9 {
        width: 2.107rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 0.32rem;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.32rem;
        margin: 0.08rem 0 0 1.68rem;
      }
      .text_10 {
        width: 2.107rem;
        height: 0.48rem;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 0.32rem;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 0.32rem;
        margin: 0.08rem 0 0.294rem 1.627rem;
      }
    }
    .box_4 {
      width: 9.36rem;
      height: 2.64rem;
      margin: 0.267rem 0 0 0.32rem;
      .section_3 {
        width: 2.48rem;
        height: 2.64rem;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNGdc7b1952f4040caabd55890af14df0e1.png)
          100% no-repeat;
        background-size: 100% 100%;
        .text-group_1 {
          width: 2rem;
          height: 2.027rem;
          margin: 0.454rem 0 0 0.187rem;
          .text-wrapper_3 {
            width: 2rem;
            height: 1.014rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            .text_11 {
              width: 2rem;
              height: 1.014rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.266rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
            }
            .text_12 {
              width: 2rem;
              height: 1.014rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.693rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.694rem;
            }
          }
          .text_13 {
            width: 0.96rem;
            height: 0.48rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.32rem;
            margin: 0.534rem 0 0 0.587rem;
          }
        }
      }
      .section_4 {
        background-color: rgba(255, 255, 255, 1);
        height: 2.64rem;
        width: 6.88rem;
        .box_5 {
          width: 6.214rem;
          height: 1.254rem;
          margin: 0.347rem 0 0 0.347rem;
          .text-group_2 {
            width: 2.454rem;
            height: 1.254rem;
            .text_14 {
              width: 2.454rem;
              height: 0.587rem;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 0.4rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.4rem;
            }
            .text_15 {
              width: 0.987rem;
              height: 0.48rem;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 0.32rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.32rem;
              margin-top: 0.187rem;
            }
          }
          .text-wrapper_4 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 5px;
            height: 0.64rem;
            margin-top: 0.294rem;
            width: 1.707rem;
            .text_16 {
              width: 1.28rem;
              height: 0.454rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.32rem;
              margin: 0.107rem 0 0 0.214rem;
            }
          }
        }
        .box_6 {
          width: 6.214rem;
          height: 0.48rem;
          margin: 0.4rem 0 0.16rem 0.347rem;
          .text_17 {
            width: 3.36rem;
            height: 0.48rem;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.32rem;
          }
          .thumbnail_5 {
            width: 0.16rem;
            height: 0.267rem;
            margin-top: 0.214rem;
          }
        }
      }
    }
    .box_7 {
      width: 9.36rem;
      height: 2.64rem;
      margin: 0.267rem 0 0 0.32rem;
      .section_5 {
        width: 2.48rem;
        height: 2.64rem;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNGa8ae8798db599178c2c049244bfc2db7.png)
          100% no-repeat;
        background-size: 100% 100%;
        .text-group_3 {
          width: 2rem;
          height: 2.027rem;
          margin: 0.454rem 0 0 0.187rem;
          .text-wrapper_5 {
            width: 2rem;
            height: 1.014rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            .text_18 {
              width: 2rem;
              height: 1.014rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.266rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
            }
            .text_19 {
              width: 2rem;
              height: 1.014rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.693rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.694rem;
            }
          }
          .text_20 {
            width: 1.6rem;
            height: 0.48rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.32rem;
            margin: 0.534rem 0 0 0.24rem;
          }
        }
      }
      .section_6 {
        height: 2.64rem;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNG4859356b7546855289788c69a3294a6b.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 6.88rem;
        .box_8 {
          width: 6.24rem;
          height: 1.254rem;
          margin: 0.347rem 0 0 0.347rem;
          .text-group_4 {
            width: 3.467rem;
            height: 1.254rem;
            .text_21 {
              width: 3.467rem;
              height: 0.587rem;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 0.4rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.4rem;
            }
            .text_22 {
              width: 1.28rem;
              height: 0.48rem;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 0.32rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.32rem;
              margin-top: 0.187rem;
            }
          }
          .section_7 {
            background-color: rgba(153, 153, 153, 1);
            width: 1.28rem;
            height: 0.96rem;
            margin-top: 0.134rem;
          }
        }
        .text-wrapper_6 {
          width: 3.36rem;
          height: 0.48rem;
          margin: 0.427rem 0 0.134rem 0.347rem;
          .text_23 {
            width: 3.36rem;
            height: 0.48rem;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.32rem;
          }
        }
      }
    }
    .box_9 {
      width: 9.36rem;
      height: 2.64rem;
      margin: 0.267rem 0 3.014rem 0.32rem;
      .box_10 {
        width: 2.48rem;
        height: 2.64rem;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNGa8ae8798db599178c2c049244bfc2db7.png)
          100% no-repeat;
        background-size: 100% 100%;
        .text-group_5 {
          width: 2rem;
          height: 2.027rem;
          margin: 0.454rem 0 0 0.187rem;
          .text-wrapper_7 {
            width: 2rem;
            height: 1.014rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            .text_24 {
              width: 2rem;
              height: 1.014rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.266rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
            }
            .text_25 {
              width: 2rem;
              height: 1.014rem;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 0.693rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.694rem;
            }
          }
          .text_26 {
            width: 1.6rem;
            height: 0.48rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.32rem;
            margin: 0.534rem 0 0 0.24rem;
          }
        }
      }
      .box_11 {
        height: 2.64rem;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNG4859356b7546855289788c69a3294a6b.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 6.88rem;
        .box_12 {
          width: 6.267rem;
          height: 1.254rem;
          margin: 0.347rem 0 0 0.347rem;
          .text-group_6 {
            width: 3.467rem;
            height: 1.254rem;
            .text_27 {
              width: 3.467rem;
              height: 0.587rem;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 0.4rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.4rem;
            }
            .text_28 {
              width: 1.28rem;
              height: 0.48rem;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 0.32rem;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.32rem;
              margin-top: 0.187rem;
            }
          }
          .text-wrapper_8 {
            background-color: rgba(153, 153, 153, 1);
            height: 0.96rem;
            margin-top: 0.187rem;
            width: 1.28rem;
            .text_29 {
              width: 0.88rem;
              height: 0.214rem;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 0.293rem;
              font-family: Source Han Sans CN-Bold;
              font-weight: 700;
              text-align: left;
              white-space: nowrap;
              line-height: 0.294rem;
              margin: 0.267rem 0 0 0.24rem;
            }
          }
        }
        .text-wrapper_9 {
          width: 3.36rem;
          height: 0.48rem;
          margin: 0.427rem 0 0.134rem 0.347rem;
          .text_30 {
            width: 3.36rem;
            height: 0.48rem;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.32rem;
          }
        }
      }
    }
    .box_13 {
      position: absolute;
      left: 0.32rem;
      top: 1.467rem;
      width: 2.48rem;
      height: 2.64rem;
      background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNGdc7b1952f4040caabd55890af14df0e1.png)
        100% no-repeat;
      background-size: 100% 100%;
      .text-group_7 {
        width: 2rem;
        height: 2.027rem;
        margin: 0.454rem 0 0 0.187rem;
        .text-wrapper_10 {
          width: 2rem;
          height: 1.014rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          .text_31 {
            width: 2rem;
            height: 1.014rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.266rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
          }
          .text_32 {
            width: 2rem;
            height: 1.014rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.693rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.694rem;
          }
        }
        .text_33 {
          width: 0.96rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.32rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.32rem;
          margin: 0.534rem 0 0 0.587rem;
        }
      }
    }
    .box_14 {
      height: 2.64rem;
      background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNG5e495abe9cd9704921ffa227bd61dae8.png)
        100% no-repeat;
      background-size: 100% 100%;
      width: 6.88rem;
      position: absolute;
      left: 2.8rem;
      top: 1.467rem;
      .block_1 {
        width: 6.214rem;
        height: 1.254rem;
        margin: 0.347rem 0 0 0.347rem;
        .text-group_8 {
          width: 2.454rem;
          height: 1.254rem;
          .text_34 {
            width: 2.454rem;
            height: 0.587rem;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 0.4rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.4rem;
          }
          .text_35 {
            width: 0.987rem;
            height: 0.48rem;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 0.32rem;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.32rem;
            margin-top: 0.187rem;
          }
        }
        .text-wrapper_11 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 5px;
          height: 0.64rem;
          margin-top: 0.294rem;
          width: 1.707rem;
          .text_36 {
            width: 1.28rem;
            height: 0.454rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.32rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.32rem;
            margin: 0.107rem 0 0 0.214rem;
          }
        }
      }
      .block_2 {
        width: 5.947rem;
        height: 0.614rem;
        margin: 0.4rem 0 0.027rem 0.347rem;
        .text_37 {
          width: 3.36rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 0.32rem;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.32rem;
        }
        .thumbnail_6 {
          width: 0.16rem;
          height: 0.267rem;
          margin-top: 0.347rem;
        }
      }
    }
  }
}
