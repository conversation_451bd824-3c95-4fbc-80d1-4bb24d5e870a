<template>
  <view class="page flex-col">
    <view class="section_1 flex-row">
      <text class="text_1">12:30</text>
      <image
        class="thumbnail_1"
        referrerpolicy="no-referrer"
        src="/static/expensedetails/FigmaDDSSlicePNGd7f3f0a8da68df76a531af0fb9fddfd7.png"
      />
      <image
        class="thumbnail_2"
        referrerpolicy="no-referrer"
        src="/static/expensedetails/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png"
      />
      <image
        class="thumbnail_3"
        referrerpolicy="no-referrer"
        src="/static/expensedetails/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png"
      />
    </view>
    <view class="section_2 flex-row">
      <image
        class="thumbnail_4"
        referrerpolicy="no-referrer"
        src="/static/expensedetails/FigmaDDSSlicePNGb2c0f043641f22ede4595acd72b9777f.png"
      />
      <text class="text_2">充值明细</text>
      <image
        class="image_1"
        referrerpolicy="no-referrer"
        src="/static/expensedetails/FigmaDDSSlicePNGb8b4476965cc16cde5ebd2a73f016520.png"
      />
    </view>
    <view class="text-wrapper_1 flex-row">
      <text class="text_3">本月</text>
      <text class="text_4">日期筛选</text>
    </view>
    <view class="list_1 flex-col">
      <view
        class="list-items_1 flex-row justify-between"
        v-for="(item, index) in loopData0"
        :key="index"
      >
        <view class="text-group_1 flex-col justify-between">
          <text class="text_5" v-html="item.lanhutext0"></text>
          <text class="text_6" v-html="item.lanhutext1"></text>
        </view>
        <view class="text-group_2 flex-col justify-between">
          <text class="text_7" v-html="item.lanhutext2"></text>
          <text class="text_8" v-html="item.lanhutext3"></text>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  data() {
    return {
      loopData0: [
        {
          lanhutext0: '0元购1千',
          lanhutext1: '2024-12-12&nbsp;16::24:32',
          lanhutext2: '+1000',
          lanhutext3: '余额：7541.00'
        },
        {
          lanhutext0: '0元购1千',
          lanhutext1: '2024-12-12&nbsp;16::24:32',
          lanhutext2: '+1000',
          lanhutext3: '余额：7541.00'
        },
        {
          lanhutext0: '0元购1千',
          lanhutext1: '2024-12-12&nbsp;16::24:32',
          lanhutext2: '+1000',
          lanhutext3: '余额：7541.00'
        },
        {
          lanhutext0: '0元购1千',
          lanhutext1: '2024-12-12&nbsp;16::24:32',
          lanhutext2: '+1000',
          lanhutext3: '余额：7541.00'
        }
      ],
      constants: {}
    };
  },
  methods: {}
};
</script>
<style lang='scss'>
@import '../common/common.scss';
@import './assets/style/index.rpx.scss';
</style>
