.page {
  background-color: rgba(247, 247, 247, 1);
  position: relative;
  width: 100vw;
  height: 216.54vw;
  overflow: hidden;
  .section_1 {
    background-color: rgba(255, 255, 255, 1);
    width: 100vw;
    height: 22.94vw;
    .box_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 100vw;
      height: 8.54vw;
      .text_1 {
        width: 8.54vw;
        height: 4.8vw;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 3.2vw;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 40vw;
        margin: 1.86vw 0 0 4.26vw;
      }
      .thumbnail_1 {
        width: 4.8vw;
        height: 4.8vw;
        margin: 1.86vw 0 0 66.93vw;
      }
      .thumbnail_2 {
        width: 4.8vw;
        height: 4.8vw;
        margin: 1.86vw 0 0 0.8vw;
      }
      .thumbnail_3 {
        width: 5.07vw;
        height: 5.07vw;
        margin: 1.86vw 4vw 0 0.8vw;
      }
    }
    .box_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 100vw;
      height: 14.4vw;
      .thumbnail_4 {
        width: 2.4vw;
        height: 4.54vw;
        margin: 5.06vw 0 0 4.8vw;
      }
      .text_2 {
        width: 17.07vw;
        height: 5.87vw;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 4.26vw;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 5.87vw;
        margin: 4.26vw 0 0 2.93vw;
      }
      .image_1 {
        width: 23.2vw;
        height: 8.54vw;
        margin: 2.93vw 1.6vw 0 48vw;
      }
    }
  }
  .section_2 {
    width: 100vw;
    height: 193.87vw;
    margin-bottom: 0.27vw;
    .list_1 {
      width: 93.6vw;
      height: 86.94vw;
      justify-content: space-between;
      margin: 2.66vw 0 0 3.2vw;
      .list-items_1 {
        background-color: rgba(255, 255, 255, 1);
        border-radius: 10px;
        width: 93.6vw;
        height: 19.74vw;
        margin-bottom: 2.67vw;
        .image-text_1 {
          width: 46.14vw;
          height: 13.34vw;
          margin: 3.2vw 0 0 3.2vw;
          .image_2 {
            width: 13.34vw;
            height: 13.34vw;
          }
          .text-group_1 {
            width: 30.4vw;
            height: 9.87vw;
            margin-top: 1.6vw;
            .text_3 {
              width: 12vw;
              height: 4vw;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 4vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26.67vw;
            }
            .text-wrapper_1 {
              width: 30.4vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              font-size: 0;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26.67vw;
              margin-top: 2.67vw;
              .text_4 {
                width: 30.4vw;
                height: 3.2vw;
                overflow-wrap: break-word;
                color: rgba(102, 102, 102, 1);
                font-size: 3.2vw;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 26.67vw;
              }
              .text_5 {
                width: 30.4vw;
                height: 3.2vw;
                overflow-wrap: break-word;
                color: rgba(11, 206, 148, 1);
                font-size: 3.2vw;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 26.67vw;
              }
              .text_6 {
                width: 30.4vw;
                height: 3.2vw;
                overflow-wrap: break-word;
                color: rgba(102, 102, 102, 1);
                font-size: 3.2vw;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 26.67vw;
              }
              .text_7 {
                width: 30.4vw;
                height: 3.2vw;
                overflow-wrap: break-word;
                color: rgba(11, 206, 148, 1);
                font-size: 3.2vw;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 26.67vw;
              }
            }
          }
        }
        .text_8 {
          width: 4.54vw;
          height: 3.2vw;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 3.2vw;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 26.67vw;
          margin: 8.26vw 3.2vw 0 36.53vw;
        }
        .image-text_2 {
          width: 8.8vw;
          height: 3.47vw;
          margin: 8vw 3.2vw 0 32.26vw;
          .thumbnail_5 {
            width: 3.47vw;
            height: 3.47vw;
          }
          .text-group_2 {
            width: 4.54vw;
            height: 3.2vw;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 3.2vw;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26.67vw;
            margin-top: 0.27vw;
          }
        }
      }
    }
  }
}
