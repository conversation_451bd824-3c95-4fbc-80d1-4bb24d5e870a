.page {
  background-color: rgba(247, 247, 247, 1);
  position: relative;
  width: 375px;
  height: 812px;
  overflow: hidden;
  .group_1 {
    background-color: rgba(255, 255, 255, 1);
    width: 375px;
    height: 86px;
    .box_1 {
      background-color: rgba(255, 255, 255, 1);
      width: 375px;
      height: 32px;
      .text_1 {
        width: 32px;
        height: 18px;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 12px;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 150px;
        margin: 7px 0 0 16px;
      }
      .thumbnail_1 {
        width: 18px;
        height: 18px;
        margin: 7px 0 0 251px;
      }
      .thumbnail_2 {
        width: 18px;
        height: 18px;
        margin: 7px 0 0 3px;
      }
      .thumbnail_3 {
        width: 19px;
        height: 19px;
        margin: 7px 15px 0 3px;
      }
    }
    .box_2 {
      background-color: rgba(255, 255, 255, 1);
      width: 375px;
      height: 54px;
      .thumbnail_4 {
        width: 9px;
        height: 17px;
        margin: 19px 0 0 18px;
      }
      .text_2 {
        width: 64px;
        height: 22px;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 16px;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 22px;
        margin: 16px 0 0 11px;
      }
      .image_1 {
        width: 87px;
        height: 32px;
        margin: 11px 6px 0 180px;
      }
    }
  }
  .group_2 {
    position: relative;
    width: 375px;
    height: 727px;
    margin-bottom: 1px;
    .box_3 {
      background-color: rgba(255, 255, 255, 1);
      height: 44px;
      width: 375px;
      .text-wrapper_1 {
        width: 295px;
        height: 22px;
        margin: 11px 0 0 40px;
        .text_3 {
          width: 45px;
          height: 22px;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 15px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 15px;
        }
        .text_4 {
          width: 45px;
          height: 22px;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 15px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 15px;
          margin-left: 80px;
        }
        .text_5 {
          width: 45px;
          height: 22px;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 15px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 15px;
          margin-left: 80px;
        }
      }
      .section_1 {
        width: 45px;
        height: 3px;
        margin: 8px 0 0 40px;
        .section_2 {
          background-color: rgba(11, 206, 148, 1);
          width: 45px;
          height: 3px;
        }
      }
    }
    .text-wrapper_2 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 0px 0px 8px 8px;
      width: 351px;
      height: 141px;
      margin: 102px 0 0 12px;
      .text_6 {
        width: 89px;
        height: 18px;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 12px;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 12px;
        margin: 22px 0 0 12px;
      }
      .text_7 {
        width: 89px;
        height: 18px;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 12px;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 12px;
        margin: 6px 0 0 12px;
      }
      .text_8 {
        width: 131px;
        height: 18px;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 12px;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 12px;
        margin: 6px 0 0 12px;
      }
      .text_9 {
        width: 79px;
        height: 18px;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 12px;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 12px;
        margin: 3px 0 0 63px;
      }
      .text_10 {
        width: 79px;
        height: 18px;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 12px;
        font-family: Source Han Sans CN-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 12px;
        margin: 3px 0 11px 61px;
      }
    }
    .box_4 {
      width: 351px;
      height: 99px;
      margin: 10px 0 0 12px;
      .section_3 {
        width: 93px;
        height: 99px;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNGdc7b1952f4040caabd55890af14df0e1.png)
          100% no-repeat;
        background-size: 100% 100%;
        .text-group_1 {
          width: 75px;
          height: 76px;
          margin: 17px 0 0 7px;
          .text-wrapper_3 {
            width: 75px;
            height: 38px;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            .text_11 {
              width: 75px;
              height: 38px;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 10px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
            }
            .text_12 {
              width: 75px;
              height: 38px;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 26px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26px;
            }
          }
          .text_13 {
            width: 36px;
            height: 18px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 12px;
            margin: 20px 0 0 22px;
          }
        }
      }
      .section_4 {
        background-color: rgba(255, 255, 255, 1);
        height: 99px;
        width: 258px;
        .box_5 {
          width: 233px;
          height: 47px;
          margin: 13px 0 0 13px;
          .text-group_2 {
            width: 92px;
            height: 47px;
            .text_14 {
              width: 92px;
              height: 22px;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 15px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 15px;
            }
            .text_15 {
              width: 37px;
              height: 18px;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 12px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 12px;
              margin-top: 7px;
            }
          }
          .text-wrapper_4 {
            background-color: rgba(11, 206, 148, 1);
            border-radius: 5px;
            height: 24px;
            margin-top: 11px;
            width: 64px;
            .text_16 {
              width: 48px;
              height: 17px;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 12px;
              margin: 4px 0 0 8px;
            }
          }
        }
        .box_6 {
          width: 233px;
          height: 18px;
          margin: 15px 0 6px 13px;
          .text_17 {
            width: 126px;
            height: 18px;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 12px;
          }
          .thumbnail_5 {
            width: 6px;
            height: 10px;
            margin-top: 8px;
          }
        }
      }
    }
    .box_7 {
      width: 351px;
      height: 99px;
      margin: 10px 0 0 12px;
      .section_5 {
        width: 93px;
        height: 99px;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNGa8ae8798db599178c2c049244bfc2db7.png)
          100% no-repeat;
        background-size: 100% 100%;
        .text-group_3 {
          width: 75px;
          height: 76px;
          margin: 17px 0 0 7px;
          .text-wrapper_5 {
            width: 75px;
            height: 38px;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            .text_18 {
              width: 75px;
              height: 38px;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 10px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
            }
            .text_19 {
              width: 75px;
              height: 38px;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 26px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26px;
            }
          }
          .text_20 {
            width: 60px;
            height: 18px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 12px;
            margin: 20px 0 0 9px;
          }
        }
      }
      .section_6 {
        height: 99px;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNG4859356b7546855289788c69a3294a6b.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 258px;
        .box_8 {
          width: 234px;
          height: 47px;
          margin: 13px 0 0 13px;
          .text-group_4 {
            width: 130px;
            height: 47px;
            .text_21 {
              width: 130px;
              height: 22px;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 15px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 15px;
            }
            .text_22 {
              width: 48px;
              height: 18px;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 12px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 12px;
              margin-top: 7px;
            }
          }
          .section_7 {
            background-color: rgba(153, 153, 153, 1);
            width: 48px;
            height: 36px;
            margin-top: 5px;
          }
        }
        .text-wrapper_6 {
          width: 126px;
          height: 18px;
          margin: 16px 0 5px 13px;
          .text_23 {
            width: 126px;
            height: 18px;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 12px;
          }
        }
      }
    }
    .box_9 {
      width: 351px;
      height: 99px;
      margin: 10px 0 113px 12px;
      .box_10 {
        width: 93px;
        height: 99px;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNGa8ae8798db599178c2c049244bfc2db7.png)
          100% no-repeat;
        background-size: 100% 100%;
        .text-group_5 {
          width: 75px;
          height: 76px;
          margin: 17px 0 0 7px;
          .text-wrapper_7 {
            width: 75px;
            height: 38px;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            .text_24 {
              width: 75px;
              height: 38px;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 10px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
            }
            .text_25 {
              width: 75px;
              height: 38px;
              overflow-wrap: break-word;
              color: rgba(255, 255, 255, 1);
              font-size: 26px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 26px;
            }
          }
          .text_26 {
            width: 60px;
            height: 18px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 12px;
            margin: 20px 0 0 9px;
          }
        }
      }
      .box_11 {
        height: 99px;
        background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNG4859356b7546855289788c69a3294a6b.png)
          100% no-repeat;
        background-size: 100% 100%;
        width: 258px;
        .box_12 {
          width: 235px;
          height: 47px;
          margin: 13px 0 0 13px;
          .text-group_6 {
            width: 130px;
            height: 47px;
            .text_27 {
              width: 130px;
              height: 22px;
              overflow-wrap: break-word;
              color: rgba(51, 51, 51, 1);
              font-size: 15px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 15px;
            }
            .text_28 {
              width: 48px;
              height: 18px;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 12px;
              font-family: Source Han Sans CN-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 12px;
              margin-top: 7px;
            }
          }
          .text-wrapper_8 {
            background-color: rgba(153, 153, 153, 1);
            height: 36px;
            margin-top: 7px;
            width: 48px;
            .text_29 {
              width: 33px;
              height: 8px;
              overflow-wrap: break-word;
              color: rgba(153, 153, 153, 1);
              font-size: 11px;
              font-family: Source Han Sans CN-Bold;
              font-weight: 700;
              text-align: left;
              white-space: nowrap;
              line-height: 11px;
              margin: 10px 0 0 9px;
            }
          }
        }
        .text-wrapper_9 {
          width: 126px;
          height: 18px;
          margin: 16px 0 5px 13px;
          .text_30 {
            width: 126px;
            height: 18px;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 12px;
          }
        }
      }
    }
    .box_13 {
      position: absolute;
      left: 12px;
      top: 55px;
      width: 93px;
      height: 99px;
      background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNGdc7b1952f4040caabd55890af14df0e1.png)
        100% no-repeat;
      background-size: 100% 100%;
      .text-group_7 {
        width: 75px;
        height: 76px;
        margin: 17px 0 0 7px;
        .text-wrapper_10 {
          width: 75px;
          height: 38px;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          .text_31 {
            width: 75px;
            height: 38px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 10px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
          }
          .text_32 {
            width: 75px;
            height: 38px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 26px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 26px;
          }
        }
        .text_33 {
          width: 36px;
          height: 18px;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 12px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 12px;
          margin: 20px 0 0 22px;
        }
      }
    }
    .box_14 {
      height: 99px;
      background: url(/static/lanhu_wodekaquan/FigmaDDSSlicePNG5e495abe9cd9704921ffa227bd61dae8.png)
        100% no-repeat;
      background-size: 100% 100%;
      width: 258px;
      position: absolute;
      left: 105px;
      top: 55px;
      .block_1 {
        width: 233px;
        height: 47px;
        margin: 13px 0 0 13px;
        .text-group_8 {
          width: 92px;
          height: 47px;
          .text_34 {
            width: 92px;
            height: 22px;
            overflow-wrap: break-word;
            color: rgba(51, 51, 51, 1);
            font-size: 15px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 15px;
          }
          .text_35 {
            width: 37px;
            height: 18px;
            overflow-wrap: break-word;
            color: rgba(102, 102, 102, 1);
            font-size: 12px;
            font-family: Source Han Sans CN-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 12px;
            margin-top: 7px;
          }
        }
        .text-wrapper_11 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 5px;
          height: 24px;
          margin-top: 11px;
          width: 64px;
          .text_36 {
            width: 48px;
            height: 17px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 12px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 12px;
            margin: 4px 0 0 8px;
          }
        }
      }
      .block_2 {
        width: 223px;
        height: 23px;
        margin: 15px 0 1px 13px;
        .text_37 {
          width: 126px;
          height: 18px;
          overflow-wrap: break-word;
          color: rgba(102, 102, 102, 1);
          font-size: 12px;
          font-family: Source Han Sans CN-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 12px;
        }
        .thumbnail_6 {
          width: 6px;
          height: 10px;
          margin-top: 13px;
        }
      }
    }
  }
}
